# Society Bill Generation Process - Visual Flowchart

## Main Process Flow

```mermaid
flowchart TD
    A[User Initiates Bill Generation] --> B{Bill Type?}
    
    B -->|Bulk Bills| C[POST /generateBulkbill]
    B -->|Manual Bill| D[GET /generatemanualbill]
    
    C --> E[IncomeDetailsController]
    D --> F[IncomeDetailsController]
    
    E --> G[generateBulkBillWorkflow]
    F --> H[generateManualBillWorkflow]
    
    G --> I[GenerateBulkBillDataSource]
    H --> J[GenerateManualBillDataSource]
    
    I --> K[ChsoneInvoiceGeneration Model]
    J --> L[Calculate Invoice Dates]
    
    K --> M[Fetch All Eligible Units]
    L --> N[Single Unit Processing]
    
    M --> O[For Each Unit]
    N --> P[generateInvoiceForSingleUnit]
    O --> P
    
    P --> Q[Load Invoice Settings]
    Q --> R[Load Unit Details]
    R --> S[Calculate Maintenance Charges]
    S --> T[Calculate Late Payment Charges]
    T --> U[Apply Tax Rules]
    U --> V[Handle Outstanding Amounts]
    V --> W[Apply Advance Adjustments]
    W --> X[Generate Invoice]
    
    X --> Y{Action Type?}
    Y -->|Preview| Z[Return Preview Data]
    Y -->|Generate| AA[Save Invoice to Database]
    
    AA --> BB[Update Payment Tracker]
    BB --> CC[Generate Invoice Number]
    CC --> DD[Create Invoice Particulars]
    DD --> EE[Apply Tax Logs]
    EE --> FF[Update Unit Invoice Status]
    
    Z --> GG[Display Preview to User]
    FF --> HH[Invoice Generated Successfully]
```

## Key Components Breakdown

```mermaid
graph TB
    subgraph DB ["Database Tables"]
        A1[income_invoice_settings]
        A2[income_invoice_general_settings]
        A3[income_late_payment_charges]
        A4[chsone_units_master]
        A5[income_unit_invoice]
        A6[income_invoice_particulars]
    end
    
    subgraph MODELS ["Models"]
        B1[ChsoneInvoiceGeneration]
        B2[IncomeInvoiceSetting]
        B3[IncomeUnitInvoice]
        B4[ChsoneUnitsMaster]
    end
    
    subgraph WF ["Workflows"]
        C1[generateBulkBillWorkflow]
        C2[generateManualBillWorkflow]
    end
    
    subgraph DS ["Data Sources"]
        D1[GenerateBulkBillDataSource]
        D2[GenerateManualBillDataSource]
    end
    
    A1 --> B2
    A2 --> B1
    A4 --> B4
    A5 --> B3
    
    B1 --> C1
    B1 --> C2
    
    C1 --> D1
    C2 --> D2
```

## Invoice Calculation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Controller
    participant W as Workflow
    participant DS as DataSource
    participant M as Model
    participant DB as Database
    
    U->>C: Request Bill Generation
    C->>W: Execute Workflow
    W->>DS: Call DataSource
    DS->>M: Invoke Model Method
    
    M->>DB: Fetch Invoice Settings
    DB-->>M: Return Settings
    
    M->>DB: Fetch Unit Details
    DB-->>M: Return Unit Data
    
    M->>M: Calculate Maintenance Charges
    M->>M: Calculate Late Payment Charges
    M->>M: Apply Tax Rules
    M->>M: Handle Outstanding Amounts
    
    M->>DB: Save Invoice Data
    DB-->>M: Confirm Save
    
    M-->>DS: Return Invoice Data
    DS-->>W: Return Response
    W-->>C: Return Result
    C-->>U: Return API Response
```

## Bill Generation Triggers

```mermaid
graph TD
    ROOT[Bill Generation Triggers] --> MANUAL[Manual Triggers]
    ROOT --> BULK[Bulk Operations]
    ROOT --> INDIVIDUAL[Individual Bills]
    ROOT --> NONE[No Automated Triggers]
    
    MANUAL --> WEB[Web Interface]
    MANUAL --> API[API Calls]
    MANUAL --> ADMIN[Admin Actions]
    
    BULK --> ALL[All Units at Once]
    BULK --> FILTERED[Filtered Units]
    BULK --> PREVIEW[Preview Mode]
    
    INDIVIDUAL --> SINGLE[Single Unit]
    INDIVIDUAL --> MEMBER[Specific Member]
    INDIVIDUAL --> OVERRIDE[Manual Override]
    
    NONE --> CRON[No Cron Jobs]
    NONE --> SCHEDULED[No Scheduled Tasks]
    NONE --> PROCESS[Manual Process Only]
```