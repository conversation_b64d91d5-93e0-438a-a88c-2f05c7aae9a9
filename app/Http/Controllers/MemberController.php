<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class MemberController extends Controller
{
    public function contactDetail(Request $request)
    {
        return $this->workflow('workflow:contactDetail', $request->all());
    }

    public function memberSignature(Request $request)
    {
        return $this->workflow('workflow:memberSignature', $request->all());
    }

    public function tenantSignature(Request $request)
    {
        return $this->workflow('workflow:tenantSignature', $request->all());
    }

    public function allotteesViewDetails(Request $request, $id)
    {
        $request = $request->merge([
            'id' => $id
        ]);
        return $this->workflow('workflow:allotteesViewDetails', $request->all());
    }

    public function allotteesViewDetailsCard(Request $request, $id)
    {
        $request = $request->merge([
            'id' => $id
        ]);
        return $this->workflow('workflow:allotteesViewDetailsCard', $request->all());
    }

    public function updateAllottees(Request $request, $id)
    {
        $request = $request->merge(['member_id' => $id]);
        return $this->workflow('workflow:createAllottees', $request->all());
    }

    public function createAllottees(Request $request)
    {
        return $this->workflow('workflow:createAllottees', $request->all());
    }

    public function bulkAddAllottees(Request $request)
    {
        return $this->workflow('workflow:bulkAddAllottees', $request->all());
    }

    public function jRegisterReportPrint(Request $request)
    {
        return $this->workflow('workflow:jRegisterReportPrint', $request->all());
    }

    public function iRegister(Request $request, $unitId)
    {
        $request = $request->merge([
            'unitId' => $unitId
        ]);
        return $this->workflow('workflow:iRegister', $request->all());
    }

    public function downloadMembers(Request $request, $type)
    {
        $request = $request->merge([
            'type' => $type
        ]);
        return $this->workflow('workflow:downloadMembers', $request->all());
    }

    public function activateDeactivate(Request $request, $type)
    {
        $request = $request->merge([
            'type' => $type
        ]);
        return $this->workflow('workflow:activateDeactivateMember', $request->all());
    }

    public function activateMember(Request $request, $member_id)
    {
        $request = $request->merge([
            'member_id' => $member_id
        ]);
        return $this->workflow('workflow:activateMember', $request->all());
    }

    public function shareCertificate(Request $request, $unit_id)
    {
        $request = $request->merge(['unit_id' => $unit_id]);
        return $this->workflow('workflow:shareCertificate', $request->all());
    }

    public function deleteMember(Request $request, $id)
    {
        $request = $request->merge(['id' => $id]);
        return $this->workflow('workflow:deleteMember', $request->all());
    }

    public function contactDetailDownloadReport(Request $request, $type)
    {
        $request->merge([
            "type" => $type
        ]);
        return $this->workflow('workflow:contactDetailDownloadReport', $request->all());
    }

    public function memberSignatureDownload(Request $request, $type)
    {
        $request->merge([
            "type" => $type
        ]);
        return $this->workflow('workflow:memberSignatureDownload', $request->all());
    }

    public function tenantSignatureDownload(Request $request, $type)
    {
        $request->merge([
            "type" => $type
        ]);
        return $this->workflow('workflow:tenantSignatureDownload', $request->all());
    }

    public function jRegisterReportPrintDownload(Request $request, $type)
    {
        $request->merge([
            "type" => $type
        ]);
        return $this->workflow('workflow:jRegisterReportPrintDownload', $request->all());
    }

    public function updateMemberUserId(Request $request, $memberId)
    {
        $request->merge([
            "id" => $memberId
        ]);
        return $this->workflow('workflow:UpdateMemberUserId', $request->all());
    }

    public function getPrimaryMember(Request $request, $unit_id) {
        $request = $request->merge(['unit_id' => $unit_id]);

        return $this->workflow('workflow:getMemberType', $request->all());

    }

    public function viewMemberShares(Request $request, $id)
    {
        $request = $request->merge(['id' => $id]);
        return $this->workflow('workflow:viewMemberShares', $request->all());
    }

    public function addShareCertificate(Request $request)
    {
        return $this->workflow('workflow:addShareCertificate', $request->all());
    }

    public function updateShareCertificate(Request $request, $unit_id, $share_certificate_id = '')
    {
        $request = $request->merge([
            'unit_id' => $unit_id,
            'share_id' => $share_certificate_id
        ]);
        return $this->workflow('workflow:updateShareCertificate', $request->all());
    }

    public function getMembersForTransferDetails(Request $request, $id)
    {
        $request = $request->merge(['id' => $id]);
        return $this->workflow('workflow:getMembersForTransferDetails', $request->all());
    }

    public function deleteMemberChangeRequest(Request $request, $id, $user_id)
    {
        $request = $request->merge(['id' => $id, 'user_id' => $user_id]);
        return $this->workflow('workflow:deleteMemberChangeRequest', $request->all());
    }

    public function approveMemberChangeRequest(Request $request, $id, $user_id)
    {
        $request = $request->merge(['id' => $id, 'user_id' => $user_id]);
        return $this->workflow('workflow:approveMemberChangeRequest', $request->all());
    }
}
