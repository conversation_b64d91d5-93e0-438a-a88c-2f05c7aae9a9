<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;
use App\Http\Traits\CommonFunctionTraits;

class NonMemberAdvancesDownload extends Workflow
{
    use CommonFunctionTraits;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:nonMemberAdvancesDownload {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Non Member Advance Download';

    protected $headings = [
        "name" => "Name",
        "total_refundable" => "Refundable Balance ₹",
        "total_adjustable" => "Adjustable Balance ₹"
    ];

    protected $summaryHeadings = [
        "total_adjustable",
        "total_refundable"
    ];

    protected $header = [];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            // Get data from nonMemberAdvance datasource
            $nonMemberAdvancesList = $this->action('datasource:nonMemberAdvance', $this->pointer, $this->request);

            // The datasource returns nested array, get the actual data
            $actualData = $nonMemberAdvancesList[0] ?? [];

            // Format data to match the headings
            $outputData = [];
            $totalRefundable = 0;
            $totalAdjustable = 0;

            foreach ($actualData as $item) {
                // Parse the formatted values back to numbers for totals
                $refundableValue = (float) str_replace(',', '', $item['total_refundable'] ?? 0);
                $adjustableValue = (float) str_replace(',', '', $item['total_adjustable'] ?? 0);

                $totalRefundable += $refundableValue;
                $totalAdjustable += $adjustableValue;

                $outputData[] = [
                    'name' => $item['non_member_name'] ?? '',
                    'total_refundable' => $this->formatNumberForExcel($refundableValue),
                    'total_adjustable' => $this->formatNumberForExcel($adjustableValue)
                ];
            }

            // Add total row
            $outputData[] = [
                'name' => 'Total',
                'total_refundable' => $this->formatNumberForExcel($totalRefundable),
                'total_adjustable' => $this->formatNumberForExcel($totalAdjustable)
            ];

            $this->data = [];
            if($type == 'excel')
            {
                // Add report heading for professional Excel format
                $reportHeading = [
                    'report_name' => 'Non Member Advance Summary',
                    'report_date_range' => ''
                ];
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'NonmemberAdvancesReport_', $reportHeading);
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($outputData, $this->headings, 'NonmemberAdvancesReport');
                $this->data['url'] = $data['data'];
            }
        }
    }

    /**
     * Format numbers for Excel to ensure zero values are displayed as "0"
     * and proper decimal formatting
     */
    private function formatNumberForExcel($value)
    {
        if($value == 0 || $value === '0')
        {
            return '0';
        }
        return round($value, 2);
    }
}
