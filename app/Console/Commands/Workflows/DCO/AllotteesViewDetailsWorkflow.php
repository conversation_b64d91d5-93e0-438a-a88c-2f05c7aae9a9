<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class AllotteesViewDetailsWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:allotteesViewDetails {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Allottees Details';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $id = $this->input['id'];

        if(!$id || $id == '' || $id == ':id')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid id';
            $this->statusCode = 400;
        }
        else{
            $allotteesDetails = $this->action('datasource:allotteesViewDetails', $this->pointer, $this->request);
            $this->data = $allotteesDetails;
            $this->meta['schema'] = $this->schema();
        }
    }

    public function schema()
    {
        return $meta = [
            "table" => [
                "tableTitle" => "Change Log",
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Name",
                        "key" => "member_name",
                    ],
                    [
                        "title" => "Email",
                        "key" => "member_email_id"
                    ],
                    [
                        "title" => "Mobile Number",
                        "key" => "member_mobile_number",
                    ],
                    [
                        "title" => "Gender",
                        "key" => "member_gender",
                    ],
                    [
                        "title" => "DOB",
                        "key" => "member_dob",
                    ],
                    [
                        "title" => "Unit",
                        "key" => "unit_flat_number"
                    ],
                    [
                        "title" => "Member Type",
                        "key" => "member_type_name"
                    ],
                    [
                        "title" => "Member W.E.F",
                        "key" => "effective_date"
                    ]
                ],
            ]
        ];
    }
}
