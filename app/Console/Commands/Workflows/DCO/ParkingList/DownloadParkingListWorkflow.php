<?php

namespace App\Console\Commands\Workflows\DCO\ParkingList;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadParkingListWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadParkingList {input}';
    
    protected $description = 'Download Parking List in Excel or PDF';

    protected $rules = [
        'type' => 'required|string|in:pdf,excel',
    ];

    protected $rulesMessage = [
        'type.required' => 'Type is required',
        'type.string' => 'Type must be a string',
        'type.in' => 'Type must be either pdf or excel',
    ];

    protected $headings = [
        "member_name" => "Member Name",
        "parking_lot" => "Parking Lot",
        "badge_number" => "Badge Number",
        "is_alloted" => "Is Alloted Parking",
        "vehicle_registration_number" => "Vehicle Num",
        "vehicle_type" => "Vehicle Type",
        "make" => "Make",
        "vehicle_colour" => "vehicle colour"
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $parkingList = $this->action('datasource:parkingList', $this->pointer, $this->request);
            $this->data = [];

            // Transform the nested data structure to flat structure for Excel
            $newData = [];

            foreach ($parkingList as $parking) {
                // Check if there are rows (vehicle registrations)
                if (!empty($parking['rows'])) {
                    foreach ($parking['rows'] as $row) {
                        // Format vehicle type
                        $vehicleType = $row['vehicle_type'] ?? '';
                        $vehicleType = ucfirst(str_replace('wheeler', 'wheeler', $vehicleType));

                        // Format is_alloted
                        $isAlloted = $row['is_alloted'] ?? '';
                        $isAlloted = ucfirst($isAlloted);

                        $newData[] = [
                            'member_name' => $row['member_name'] ?? '',
                            'parking_lot' => $row['parking_number'] ?? '',
                            'badge_number' => $row['badge_number'] ?? '',
                            'is_alloted' => $isAlloted,
                            'vehicle_registration_number' => $row['vehicle_registration_number'] ?? '',
                            'vehicle_type' => $vehicleType,
                            'make' => trim($row['make'] ?? ''),
                            'vehicle_colour' => $row['vehicle_colour'] ?? '',
                        ];
                    }
                } else {
                    // If no rows, add the main parking entry
                    $newData[] = [
                        'member_name' => $parking['member_name'] ?? '',
                        'parking_lot' => '',
                        'badge_number' => '',
                        'is_alloted' => '',
                        'vehicle_registration_number' => '',
                        'vehicle_type' => '',
                        'make' => '',
                        'vehicle_colour' => '',
                    ];
                }
            }

            if($type == 'excel')
            {
                // Add report heading for professional Excel format
                $reportHeading = [
                    'report_name' => 'Parking List',
                    'report_date_range' => ''
                ];
                
                $data = $this->hitCURLForGenerateCSV($newData, $this->headings, 'parkingList_', $reportHeading);
                $this->data['url'] = $data['data'];
            }
            else
            {
                $data = $this->hitCURLForGeneratePDF($parkingList, $this->headings, 'parkingList');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
