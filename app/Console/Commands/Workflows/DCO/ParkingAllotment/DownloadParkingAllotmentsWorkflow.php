<?php

namespace App\Console\Commands\Workflows\DCO\ParkingAllotment;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadParkingAllotmentsWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadParkingAllotments {input}';
    
    protected $description = 'Download Parking Allotments in Excel or PDF';

    protected $rules = [
        'type' => 'required|string|in:pdf,excel',
    ];

    protected $rulesMessage = [
        'type.required' => 'Type is required',
        'type.string' => 'Type must be a string',
        'type.in' => 'Type must be either pdf or excel',
    ];

    protected $headings = [
        "id" => "Sr. No.",
        "parking_number" => "Parking Number",
        "allotment_to" => "Alloted To",
        "parking_type" => "Type",
        "effective_date" => "w.e.f.",
        "upto" => "upto",
        "allowed_number_of_parkings" => "Allowed",
        "actual_number_of_parkings" => "Actual",
        "allotment_for" => "Space for"
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $parkingAllotmentList = $this->action('datasource:parkingAllotmentList', $this->pointer, $this->request);
            $this->data = [];

            // Transform the data to match our headings
            $count = 0;
            $newData = [];
            $totalAllowed = 0;
            $totalActual = 0;

            foreach ($parkingAllotmentList as $parking) {
                $count++;

                // Format the effective date to remove "onwards"
                $effectiveDate = str_replace(' onwards', '', $parking['effective_date'] ?? '');

                // Format allotment for (e.g., "2wheeler" -> "2 Wheeler")
                $allotmentFor = $parking['allotment_for'] ?? '';
                $allotmentFor = ucfirst(str_replace('wheeler', ' Wheeler', $allotmentFor));

                // Format parking type
                $parkingType = $parking['parking_type'] ?? '';
                if (empty($parkingType)) {
                    $parkingType = $parking['allotted_parking_type'] ?? 'parking';
                }

                $allowed = (int)($parking['allowed_number_of_parkings'] ?? 0);
                $actual = (int)($parking['actual_number_of_parkings'] ?? 0);

                $totalAllowed += $allowed;
                $totalActual += $actual;

                $newData[] = [
                    'id' => sprintf('%04d', $count), // Format as 0001, 0002, etc.
                    'parking_number' => $parking['parking_number'] ?? '',
                    'allotment_to' => $parking['unit_name'] ?? '',
                    'parking_type' => $parkingType,
                    'effective_date' => $effectiveDate,
                    'upto' => '', // Empty as per screenshot
                    'allowed_number_of_parkings' => $allowed,
                    'actual_number_of_parkings' => $actual,
                    'allotment_for' => $allotmentFor,
                ];
            }

            // Add total row
            $newData[] = [
                'id' => 'Total',
                'parking_number' => '',
                'allotment_to' => '',
                'parking_type' => count($parkingAllotmentList),
                'effective_date' => '',
                'upto' => '',
                'allowed_number_of_parkings' => $totalAllowed,
                'actual_number_of_parkings' => $totalActual,
                'allotment_for' => '',
            ];

            if($type == 'excel')
            {
                // Add report heading for professional Excel format
                $reportHeading = [
                    'report_name' => 'Parking Allotment List',
                    'report_date_range' => ''
                ];

                $data = $this->hitCURLForGenerateCSV($newData, $this->headings, 'parkingAllotment_', $reportHeading);
                $this->data['url'] = $data['data'];
            }
            else
            {
                $data = $this->hitCURLForGeneratePDF($parkingAllotmentList, $this->headings, 'parkingAllotment');
                $this->data['url'] = $data['data'];
            }
        }
    }

    public function concat($a, $b, $c=null)
    {
        if (!is_null($c)) {
            $c = ucfirst(str_replace('wheeler', ' Wheeler', $c));
            return "Allowed: ".$a . ', Actual: ' . $b. ", Space for: ". $c;
        } elseif (empty($b)) {
            return $a ." onwards";
        } else {
            return $a ." / ".$b;
        }
    }
}