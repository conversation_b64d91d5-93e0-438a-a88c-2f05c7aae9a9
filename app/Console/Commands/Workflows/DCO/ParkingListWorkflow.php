<?php

namespace App\Console\Commands\Workflows\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class ParkingListWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:parkingList {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Parking List';

    protected $schema = [
        "table" => [
                "tableTitle" => "Vehicle Registration",
                "select_by" => [
                    "memberName" => "Member Name",
                    "badgeNumber" => "Badge Number",
                    "vehicleRegNumber" => "Vehicle Registration Number",
                    "vehicleType" => "Vehicle Type"
                ],
                "filter_by" => [
                    "is_alloted" => [
                        "title" => "Is Alloted?",
                        "options" => [
                            "yes" => "Yes",
                            "no" => "No"
                        ],
                        "select_single" => true
                    ]
                ],
                "actions" => [
                    [
                        "title" => "New Vehicle",
                        "icon" => "ri-add-circle-line",
                        "color" => "primary",
                        "redirect" => "/admin/parking/registervehicle",
                        "variant" => "contained"
                    ],
                    [
                        "title" => "Print/Export",
                        "icon" => "ri-printer-line",
                        "options" => [
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "url" => "/admin/parking/list/download/excel",
                                    "method" => "GET",
                                    "type" => "download"
                                ]
                            ],
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" => [
                                    "method" => "GET",
                                    "url" => "/admin/parking/list/download/pdf",
                                    "type" => "download"
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        "title" => "Owner",
                        "key" => "member_name",
                        "hide_on" => [
                            "disable" => [
                                1
                            ]
                        ],
                    ],
                    [
                        "title" => "Parking Number",
                        "key" => "parking_number"
                    ],
                    [
                        "title" => "Badge Number",
                        "key" => "badge_number",
                    ],
                    [
                        "title" => "Is Alloted",
                        "key" => "is_alloted"
                    ],
                    [
                        "title" => "Vehicle Number",
                        "key" => "vehicle_registration_number"
                    ],
                    [
                        "title" => "Vehicle Type",
                        "key" => "vehicle_type"
                    ],
                    [
                        "title" => "Vehicle Details",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "Make:",
                                "key" => "make",
                                "type" => "text",
                                "show_on" => [
                                    "disable" => [0]
                                ],
                            ],
                            [
                                "title" => "Colour:",
                                "key" => "vehicle_colour",
                                "type" => "text",
                                "show_on" => [
                                    "disable" => [0]
                                ],
                            ],
                            [
                                "title" => "Edit & View",
                                "action_text" => "edit",
                                "key" => "edit",
                                "type"  => "link",
                                "show_on" => [
                                    "disable" => [1]
                                ],
                                "href" => "/admin/parking/registervehicle/:id",
                            ]
                        ]
                    ]
                ]
            ]
    ];

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $parkingAllotmentList = $this->action('datasource:parkingList', $this->pointer, $this->request);
        $this->data = $parkingAllotmentList;
        $this->meta['schema'] = $this->schema;
    }

    public function getParkingNumber($parking_number, $soc_building_name)
    {
        return $soc_building_name . '-' . $parking_number;
    }

    public function getBuildingUnitName($unit_id, $soc_building_name, $unit_flat_number)
    {
        return $soc_building_name . '-' . $unit_flat_number;
    }
}
