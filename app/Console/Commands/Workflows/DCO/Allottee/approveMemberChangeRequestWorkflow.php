<?php

namespace App\Console\Commands\Workflows\DCO\Allottee;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class approveMemberChangeRequestWorkflow extends Workflow
{
    protected $signature = 'workflow:approveMemberChangeRequest {input}';

    protected $description = 'Approve Member Change Request Workflow';

    protected $rules = [
        'id' => 'required',
        'user_id' => 'required'
    ];

    protected $rulesMessage = [
        'id.required' => 'Please provide a valid id',
        'user_id.required' => 'Please provide a valid user id'
    ];

    public function apply()
    {
        $id = $this->input['id'];

        if (!$id || $id == '' || $id == ':id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid member id';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $approveMemberChangeRequest = $this->action('datasource:approveMemberChangeRequest', $this->pointer, $this->request);
        }
    }
}