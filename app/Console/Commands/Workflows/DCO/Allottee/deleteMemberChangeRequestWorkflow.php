<?php

namespace App\Console\Commands\Workflows\DCO\Allottee;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class deleteMemberChangeRequestWorkflow extends Workflow
{
    protected $signature = 'workflow:deleteMemberChangeRequest {input}';

    protected $description = 'Delete Member Change Request Workflow';

    protected $rules = [
        'id' => 'required',
        'user_id' => 'required'
    ];

    protected $rulesMessage = [
        'id.required' => 'Please provide a valid id',
        'user_id.required' => 'Please provide a valid user id'
    ];

    public function apply()
    {
        $id = $this->input['id'];

        if (!$id || $id == '' || $id == ':id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid member id';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $deleteMemberChangeRequest = $this->action('datasource:deleteMemberChangeRequest', $this->pointer, $this->request);
        }
    }
}