<?php

namespace App\Console\Commands\Workflows\DCO\VehicleRegistration;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class VehicleRegisterByIdWorkflow extends Workflow
{
    protected $signature = 'workflow:vehicleRegisterById {input}';

    protected $description = 'Vehicle Register By Id';

    protected $schema = [
        "table" => [
            "tableTitle" => "New / Edit Vehicles Registration",
            "actions" => [
              [
                "title" => "Back",
                "redirect" => "/admin/parking/list",
                "variant" => "contained",
                "go_back" => true
              ]
            ],
            "fields" => [
                "*"
            ],
            "edit_mode" => "row",
            "add_row"=>true,
            "columns" => [
                [
                  "title" => "#",
                  "key" => "id",
                ],
                [
                  "title" => "Owner",
                  "key" => "fk_member_id",
                  "editable" => true,
                  "type" => "select",
                  "api_path" => "/admin/parking/registervehicle/memberList/:unit_id?per_page=300",
                  "label" => "member_name",
                ],
                [
                  "title" => "Parking Allot",
                  "key" => "fk_parking_unit_id",
                  "editable" => true,
                  "type" => "select",
                  "api_path" => "/admin/parking/registervehicle/allotmentList/:unit_id?per_page=300",
                  "label" => "parking_unit_name",
                ],
                [
                  "title" => "Badge number",
                  "key" => "badge_number",
                  "editable" => true
                ],
                [
                  "title" => "Reg. num",
                  "key" => "vehicle_registration_number",
                  "editable" => true
                ],
                [
                    "title" => "Type",
                    "key" => "vehicle_type",
                    "editable" => true,
                    "type" => "select",
                    "options" => [
                        [
                          "value" => "2wheeler",
                          "label" => "2wheeler"
                        ],
                        [
                          "value" => "4wheeler",
                          "label" => "4wheeler"
                        ]
                    ]
                ],
                [
                    "title" => "Reg. Type",
                    "key" => "vehicle_registration_type",
                    "editable" => true,
                    "type" => "select",
                    "options" => [
                        [
                          "value" => "primary",
                          "label" => "Primary"
                        ],
                        [
                          "value" => "alternate",
                          "label" => "Alternate"
                        ]
                    ]
                ],
                [
                    "title" => "Comp",
                    "key" => "vehicle_company",
                    "editable" => true
                ],
                [
                    "title" => "Model Num",
                    "key" => "vehicle_model_number",
                    "editable" => true
                ],
                [
                    "title" => "Colour",
                    "key" => "vehicle_colour",
                    "editable" => true
                ],
                [
                    "title" => "Allowed from",
                    "key" => "effective_date",
                    "editable" => true,
                    "type" => "date"
                ],
                [
                  "title" => "Actions",
                  "type" => "actions",
                  "key" => "actions",
                  "size" => "120px",
                  "actions" => [
                    [
                      "title" => "Edit Vehicle",
                      "icon" => "ri-edit-box-line",
                      "isedit" => true,
                      "_action" => [
                                "add"
                      ]
                      // "api" => [
                      //   "method" => "put",
                      //   "url" => "/admin/parking/registervehicle/:id",
                      // ],
                    ],
                    [
                      "title" => "Delete Vehicle",
                      "icon" => "ri-delete-bin-6-line",
                      "api" => [
                        "method" => "delete",
                        "url" => "/admin/parking/registervehicle/:id",
                      ],
                    ],
                  ]
                ]
            ]
        ]
    ];

    public function apply()
    {
        $id = $this->input['id'];

        if (!$id || $id == '' || $id == ':id') {
            $this->status = 'error';
            $this->message = 'Please provide a valid id';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $vehicle = $this->action('datasource:vehicleRegisterById', $this->pointer, $this->request);
            $this->meta['schema'] = $this->schema;
            if(count($vehicle) > 0)
            {
              $this->meta['schema']['table']['tableTitle'] = 'Edit Vehicles Registration'.' / '.$vehicle[0]['soc_building_name'] . '-' . $vehicle[0]['unit_flat_number'];
              $this->meta['schema']['table']['columns'][1]['api_path'] = "/admin/parking/registervehicle/memberList/".$vehicle[0]['unit_id'];
              $this->meta['schema']['table']['columns'][2]['api_path'] = "/admin/parking/registervehicle/allotmentList/".$vehicle[0]['unit_id'];
            } else {
              $this->meta['schema']['table']['tableTitle'] = 'New Vehicles Registration';
            }
        }
    }
}
