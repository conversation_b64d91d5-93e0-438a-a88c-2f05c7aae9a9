<?php

namespace App\Console\Commands\Workflows\DCO\CommitteeMember;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class CommitteeDetailsCommitteeMembersWorkflow extends Workflow
{
    protected $signature = 'workflow:committeeDetailsCommitteeMembers {input}';

    protected $description = 'Committee Details Committee Members';

    protected $schema = [
        "table" => [
            "tableTitle" => [
                "Office Bearer",
                "Committee Members"
            ],
             "actions" => [
                [
                    "title" => "Dissolve Committee",
                    "icon" => "ri-close-line",
                    "color" => "error",
                    "form" => "dissolve_committee",
                    "variant" => "contained"
                ],
                [
                    "title" => "Back",
                    "icon" => "ri-arrow-left-line",
                    "redirect" => "/admin/committees/list",
                    "variant" => "contained"
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    [
                        "title" => "Responsibility",
                        "key" => "ob_designation_name",
                        "type" => "chip",
                        "options" => [
                            "T" => [
                                "title" => "Treasurer",
                                "color" => "success"
                            ],
                            "S" => [
                                "title" => "Secretary",
                                "color" => "warning"
                            ],
                            "C" => [
                                "title" => "Committee Member",
                                "color" => "default"
                            ],
                            "C" => [
                                "title" => "Chairman",
                                "color" => "info"
                            ],
                        ],
                    ],
                    [
                        "title" => " Members Name",
                        "key" => "ob_member_name",
                    ]
                ],
                [
                    [
                        "title" => "Members Name",
                        "key" => "member_name"
                    ]
                ]
                
            ]
        ]
    ];

    public function apply()
    {
        $id = $this->input['id'];

        if(!$id || $id == '' || $id == ':id')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid id';
            $this->statusCode = 400;
        }
        else{
            $committeeDetails = $this->action('datasource:committeeDetailsCommitteeMembers', $this->pointer, $this->request);           
            $this->meta['schema'] = $this->schema;
        }

       $id = $this->input['id'];
       $this->meta['schema']['table']['actions'][0]['form'] = "dissolve_committee?id=$id";
    }
}