<?php

namespace App\Console\Commands\Workflows\Account;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class DownloadBankAccountsWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadBankAccounts {input}';

    protected $description = 'Download Bank Accounts in excel or pdf format';



    protected $formatterByKeys = ['id'];

    protected $headings = [
        'id'=>'Sr No.',
        'bank_name'=>'Cash Account Name',
        'ledger_account_name'=>'Ledger Name',
        'account_number'=>'A/c No.',
        'bank_address'=>'Bank Address',
        'branch'=>'Bank City',
    ];

    protected $rules = [
        'type' => 'required|string|in:pdf,excel',
    ];

    protected $rulesMessage = [
        'type.required' => 'Type is required',
        'type.string' => 'Type must be a string',
        'type.in' => 'Type must be either pdf or excel',
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{

            $viewBankAccountsList = $this->action('datasource:viewBankAccountsList', $this->pointer, $this->request);
            $this->data = [];

          //map the viewBankAccountsList data to the formatter bank_name,,ledger_account_name,account_number,bank_address,branch
          $count = 0;
          $newData = [];

          // Transform each bank account record - the datasource returns data directly
          foreach ($viewBankAccountsList as $account) {
              $count++;
              $newData[] = [
                  'id' => $count,
                  'bank_name' => $account->bank_name ?? '',
                  'ledger_account_name' => $account->ledger_account_name ?? '',
                  'account_number' => $account->account_number ?? '',
                  'bank_address' => $account->bank_address ?? '',
                  'branch' => $account->branch ?? '',
              ];
          }

          if ($type == 'excel') {
              $data = $this->hitCURLForGenerateCSV($newData, $this->headings, 'bank_accounts_');
              $this->data['url'] = $data['data'];
          }
            else{
              $data = $this->hitCURLForGeneratePDF($viewBankAccountsList, $this->headings, 'viewBankAccount');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
