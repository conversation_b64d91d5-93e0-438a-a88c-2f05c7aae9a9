<?php

namespace App\Console\Commands\Workflows\Account\FinancialStatements;

use App\Console\Commands\Workflow;
use App\Http\Traits\CommonFunctionTraits;

class DownloadBalanceSheetWorkflow extends Workflow {
    /**
    * The name and signature of the console command.
    *
    * @var string
    */

    use CommonFunctionTraits;

    protected $signature = 'workflow:downloadBalanceSheet {input}';

    /**
    * The console command description.
    *
    * @var string
    */
    protected $description = 'Balance Sheet';

    /**
    * Execute the console command.
    */

    protected $schema = [
        'table' => [
            'fields' => [
                '*'
            ],
            'columns' => [
                [
                    [
                        'title' => 'Liability',
                        'key' => 'liability'
                    ],
                ],
                [
                    [
                        'title' => 'Asset',
                        'key' => 'asset'
                    ],
                ]
            ],
        ]
    ];
   
    protected $rules=[
        'method'=>'required|string|in:excel,pdf'
    ];

    protected $rulesMessage=[
        "method.required"=>"Please provide a valid type: either excel or pdf",
        'method.in'=>'Please provide a valid type: either excel or pdf'
    ];

    public function apply() {
        $method = $this->input[ 'method' ];
        $year = $this->input['lstYear'] ?? null;

        // Validate the input type
        if ( !$method || $method == '' || $method == ':type' ) {
            $this->status = 'error';
            $this->message = 'Please provide a valid type: either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        $financial_year = $this->input['lstYear'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $type = $this->input['type'] ?? 'yearly';
        $month = $this->input['lstMonth'] ?? 3;
        $asondateval = $this->input['asondateval'] ?? date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $inputValidation = $this->validateInput(array(
            "financial_year"=>$financial_year,
            "type"=>$type,
            "month"=>$month,
            "asondateval"=>$asondateval,
            "soc_id"=>$soc_id
        ));

        if ($inputValidation["status"] == "error") {
            $this->message = $inputValidation["message"];
            $this->status = $inputValidation["status"];
            $this->statusCode = 400;
            return;
        }


        // Fetch data from the action
        $data = $this->action( 'action:balanceSheet', $this->pointer, $this->request );

        // Prepare financial year keys
        $financial_year = $this->input[ 'lstYear' ] ?? date( 'Y' ) . '-' . ( date( 'Y' ) + 1 );
        $years = explode( '-', $financial_year );
        $prev_financial_year = ( $years[ 0 ] - 1 ) . '_' . $years[ 0 ];
        $current_financial_year = $years[ 0 ] . '_' . $years[ 1 ];

        // Flatten nested arrays
        $data1 = isset( $data[ 0 ] ) && is_array( $data[ 0 ] )
        ? $this->flattenNestedArray( $data[ 0 ], $prev_financial_year, $current_financial_year )
        : [];
        $data2 = isset( $data[ 1 ] ) && is_array( $data[ 1 ] )
        ? $this->flattenNestedArray( $data[ 1 ], $prev_financial_year, $current_financial_year )
        : [];

        // Define headings
        $headings1 = [ 'Liabilities', $prev_financial_year, 'During This Year', $current_financial_year ];
        $headings2 = [ 'Assets', $prev_financial_year, 'During This Year', $current_financial_year ];

        // Merge tables
        $mergedData = $this->mergeTablesWithBlankColumn( $headings1, $data1, $headings2, $data2 );
        $mergedDataForPDF = $this->mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2);
        $newData = [];

        // dd($mergedDataForPDF);
        foreach ($mergedDataForPDF['data'] as $row) {
            $newData[] = [
                'ledger_account_name' => $row[0] ?? '-',  // First column
                "start_year_1" => $row[1] ?? 0,      // Second column
                "during_this_year_1" => $row[2] ?? 0,      // Third column
                'end_year_1' => $row[3] ?? 0,      // Third column
                'ledger_account_name1' => $row[5] ?? '-',  // First column
                'start_year_2' => $row[6] ?? 0,      // Second column
                "during_this_year_2" => $row[7] ?? 0,      // Third column
                'end_year_2' => $row[8] ?? 0,
            ];
        }

        $companyDeatils = $this->action('datasource:company', $this->pointer, $this->request);

        $companyDeatils[0]['year'] = 'Balance Sheet for ' . $year;

        // Set schema and data
        $this->meta[ 'schema' ] = $this->schema;
        $this->data = $mergedData[ 'data' ];
        // Set merged data only

        // Generate output based on type
        if ( $method === 'excel' ) {
            $result = $this->hitCURLForGenerateCSV( $mergedData[ 'data' ], $mergedData[ 'headings' ], 'Balance_Sheet_' );
        } else {
            $result = $this->hitCURLForGeneratePDF( $mergedData, [], 'balanceSheet_');
        }

        $this->data[ 'url' ] = $result[ 'data' ];
    }

    public function flattenNestedArray( $nestedArray, $prev_financial_year, $current_financial_year, &$flatArray = [] ) {
        foreach ( $nestedArray as $item ) {
            // Check if the item meets the criteria to be added to the flat array
            if ( is_array( $item ) && isset( $item[ 'id' ] ) && isset( $item[ 'ledger_account_name' ] ) ) {
                $flatArray[] = [
                    $item[ 'ledger_account_name' ] ?? '',
                    $item[ $prev_financial_year ] ?? 0,
                    $item[ 'during_this_year' ] ?? 0,
                    $item[ $current_financial_year ] ?? 0,
                ];
            }

            // Process nested rows recursively, only if they are valid arrays
            if ( !empty( $item[ 'rows' ] ) && is_array( $item[ 'rows' ] ) ) {
                $this->flattenNestedArray( $item[ 'rows' ], $prev_financial_year, $current_financial_year, $flatArray );
            }
        }

        return $flatArray;
    }

    public function mergeTablesWithBlankColumn( $headings1, $data1, $headings2, $data2 ) {
        // Combine headings with a blank column in between
        $mergedHeadings = array_merge( $headings1, [ '' ], $headings2 );

        // Determine the maximum number of rows between the two datasets
        $maxRows = max( count( $data1 ), count( $data2 ) );
        $mergedData = [];

        for ( $i = 0; $i < $maxRows; $i++ ) {
            // Retrieve data from dataset 1 or fill with empty cells
            $row1 = $i < count( $data1 ) ? $data1[ $i ] : array_fill( 0, count( $headings1 ), '' );

            // Retrieve data from dataset 2 or fill with empty cells
            $row2 = $i < count( $data2 ) ? $data2[ $i ] : array_fill( 0, count( $headings2 ), '' );

            // Combine rows with a blank column in between
            $mergedData[] = array_merge( $row1, [ '' ], $row2 );
        }

        return [ 'headings' => $mergedHeadings, 'data' => $mergedData ];
    }

}
