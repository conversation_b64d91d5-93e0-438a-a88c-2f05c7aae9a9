<?php

namespace App\Console\Commands\Workflows\Account\FinancialStatements;

use App\Console\Commands\Workflow;
use App\Http\Traits\CommonFunctionTraits;

class DownloadCashflowWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:downloadCashFlow {input}';
    use CommonFunctionTraits;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cash Flow Statement';

    /**
     * Execute the console command.
     */

     protected $rules=[
        'method'=>'required|string|in:excel,pdf'
    ];

    protected $rulesMessage=[
        "method.required"=>"Please provide a valid type: either excel or pdf",
        'method.in'=>'Please provide a valid type: either excel or pdf'
    ];

    public function apply()
    {
        $method = $this->input[ 'method' ];
        $year = $this->input['lstYear'] ?? null;
        $financial_year = $this->input['lstYear'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $type = $this->input['type'] ?? 'yearly';
        $month = $this->input['lstMonth'] ?? 3;
        $asondateval = $this->input['asondateval'] ?? date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $inputValidation = $this->validateInput(array(
            "financial_year"=>$financial_year,
            "type"=>$type,
            "month"=>$month,
            "asondateval"=>$asondateval,
            "soc_id"=>$soc_id
        ));

        if ($inputValidation["status"] == "error") {
            $this->message = $inputValidation["message"];
            $this->status = $inputValidation["status"];
            $this->statusCode = 400;
            return;
        }

        // Validate the input type
        if (!$method || $method == '' || $method == ':type') {
            $this->status = 'error';
            $this->message = 'Please provide a valid type: either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // Fetch data from the action
        $data = $this->action('action:cashFlow', $this->pointer, $this->request);
        $this->data = $data;
        // Prepare financial year keys

        // Flatten nested arrays
        $data1 = isset($data[0]) && is_array($data[0])
            ? $this->flattenNestedArray($data[0],)
            : [];
        $data2 = isset($data[1]) && is_array($data[1])
            ? $this->flattenNestedArray($data[1],)
            : [];

        // dd($data[0]);

        // Define headings
        $headings1 = ['Inflow', 'amount'];
        $headings2 = ['Outflow', 'amount'];

        // Merge tables
        $mergedDataForExcel = $this->mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2);
        $mergedDataForPDF = $this->mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2);
        $newData = [];

        // dd($mergedDataForPDF);
        foreach ($mergedDataForPDF['data'] as $row) {
            $newData[] = [
                'ledger_account_name' => $row[0] ?? '-',  // First column
                'amount' => $row[1] ?? 0,      // Second column
                'ledger_account_name1' => $row[3] ?? 0,      // Third column
                'amount1' => $row[4] ?? '-', // Fifth column
            ];
        }

        $companyDeatils = $this->action('datasource:company', $this->pointer, $this->request);

        $companyDeatils[0]['year'] = 'Cash Flow for ' . $year;

        // Set schema and data
        $this->data = $mergedDataForExcel['data'];
        // Set merged data only

        // Generate output based on type
        if ($method === 'excel') {
            $result = $this->hitCURLForGenerateCSV($mergedDataForExcel['data'], $mergedDataForExcel['headings'], 'Cash_Flow_');
        } else {
            $result = $this->hitCURLForGeneratePDF($newData, [], 'cashflow');
        }

        $this->data['url'] = $result['data'];
    }

    public function flattenNestedArray($nestedArray, &$flatArray = [])
    {
        foreach ($nestedArray as $item) {
            // Check if the item meets the criteria to be added to the flat array
            if (is_array($item) && isset($item['ledger_account_name'])) {
                $flatArray[] = [
                    $item['ledger_account_name'] ?? '',
                    $item['amount'] ?? 0,
                ];
            }

            // Process nested rows recursively, only if they are valid arrays
            if (!empty($item['rows']) && is_array($item['rows'])) {
                $this->flattenNestedArray($item['rows'], $flatArray);
            }
        }

        return $flatArray;
    }

    public function mergeTablesWithBlankColumn($headings1, $data1, $headings2, $data2)
    {
        // Combine headings with a blank column in between
        $mergedHeadings = array_merge($headings1, [''], $headings2);

        // Determine the maximum number of rows between the two datasets
        $maxRows = max(count($data1), count($data2));
        $mergedData = [];

        for ($i = 0; $i < $maxRows; $i++) {
            // Retrieve data from dataset 1 or fill with empty cells
            $row1 = $i < count($data1) ? $data1[$i] : array_fill(0, count($headings1), '');

            // Retrieve data from dataset 2 or fill with empty cells
            $row2 = $i < count($data2) ? $data2[$i] : array_fill(0, count($headings2), '');

            // Combine rows with a blank column in between
            $mergedData[] = array_merge($row1, [''], $row2);
        }

        return ['headings' => $mergedHeadings, 'data' => $mergedData];
    }
}
