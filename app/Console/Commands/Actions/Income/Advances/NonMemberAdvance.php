<?php

namespace App\Console\Commands\Actions\Income\Advances;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneCreditAccount;
use App\Models\Tenants\ChsoneNonmemberMaster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

// STEP 8 : Create a new action class whitch extends Action class. Keep signature  same as you specified in workflow.
class NonMemberAdvance extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:nonMemberAdvance {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Non Member Advances List';

    // STEP 9: Specified formatter for your action. This will format your data as per your requirement.
    // Formatter will 3 level depth array. 1st level for outer array and 2nd level for inner array and so on.
    protected $formatterByKeys =  ['id'];

    protected $schema = [
        "table" => [
            "tableTitle" => "Advances - ",
            "is_searchable" => true,
            "options" => [
                [
                    "title" => "Members",
                    "redirect" => "/admin/credit-accounts/memberAdvances"
                ],
                [
                    "title" => "Non-Members",
                    "redirect" => "/admin/credit-accounts/nonMemberAdvances",
                    "selected" => true
                ]
            ],
            "select_by" => [
                "non_member_name" => "Non-member Name"
            ],
            "actions" => [
                [
                    "title" => "New Advances",
                    "icon" => "ri-add-circle-line",
                    "color" => "primary",
                    "redirect" => "/admin/credit-accounts/add",
                    "variant" => "contained"
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    [
                        "title" => "Name",
                        "key" => "non_member_name"
                    ],
                    [
                        "title" => "Refundable Balance ₹",
                        "key" => "total_refundable",
                    ],
                    [
                        "title" => "Adjustable Balance ₹",
                        "key" => "total_adjustable"
                    ],
                    [
                        "title" => "Balance ₹",
                        "key" => "total_balance"
                    ],
                    [
                        "title" => "Actions",
                        "type" => "actions",
                        "key" => "actions",
                        "actions" => [
                            [
                                "title" => "View",
                                "icon" => "ri-eye-line",
                                "href" => "/admin/credit-accounts/viewAllTransactions/nonmember/:id",
                                "color" => "primary",
                                "marginRight" => "5px"
                            ],
                            [
                                "title" => "Refund Money",
                                "icon" => "ri-loop-left-line",
                                //"href" => "/admin/credit-accounts/nonmemberAdvances",
                                "form" => "vendorAdvanceRefundMoney?type=nonmember",
                                "color" => "primary",
                                "marginRight" => "5px",
                                "disable_on" => [
                                    "disable" => [
                                        true
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ];

    protected $schema1 = [
        "table" => [
            "tableTitle" => [
                "Non Member Advance",
                "Summary"
            ],
            "select_by" => [
                "non_member_name" => "Non-member Name"
            ],
            "actions" => [
                [
                    "title" => "Print/Export",
                    "icon" => "ri-printer-line",
                    "options" => [
                        [
                            "title" => "Excel",
                            "icon" => "ri-file-excel-2-line",
                            "api" => [
                                "url" => "/admin/credit-accounts/nonMemberAdvances/download/excel",
                                "method" => "GET",
                                "type" => "download"
                            ]
                        ],
                        [
                            "title" => "PDF",
                            "icon" => "ri-file-pdf-2-line",
                            "api" => [
                                "method" => "GET",
                                "url" => "/admin/credit-accounts/nonMemberAdvances/download/pdf",
                                "type" => "download"
                            ]
                        ]
                    ]
                ]
            ],
            "fields" => [
                "*"
            ],
            "columns" => [
                [
                    [
                        "title" => "Name",
                        "key" => "non_member_name"
                    ],
                    [
                        "title" => "Refundable Balance ₹",
                        "key" => "total_refundable",
                    ],
                    [
                        "title" => "Adjustable Balance ₹",
                        "key" => "total_adjustable"
                    ],
                ], [
                    [
                        'title' => "Refundable Balance",
                        'key' => "total_summary_refundable",
                    ],
                    [
                        'title' => "Adjustable Balance",
                        'key' => "total_summary_adjustable",
                    ],
                ]
            ]
        ]
    ];

    

    protected $formatter = [
        "id" => "",
        "first_name" => "",
        "last_name" => "",
        "non_member_name" => "", //"concat:first_name,last_name",
        "total_cr" => "",
        "total_dr" => "",
        "total_refundable_dr" => "",
        "total_refundable_cr" => "",
        "total_adjustable_dr" => "",
        "total_adjustable_cr" => "",
        "total_adjustable" => "",
        "total_refundable" => "",
        "total_balance" => "",
        "disable" => "checkDisable:total_refundable_cr,total_refundable_dr"
    ];

    protected $mapper = [
        'id' => "cr_acc.account_id",
        'nonMemberName' => 'nonmembermaster.first_name'
    ];
    /**
     * Execute the console command.
     */
    public function apply()
    {

        $current_option = "Non-Members";
        $non_member_name = '';
        $page = $this->input['page'] ?? 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        $searchTerm = $this->input['filters']['search'] ?? '';

        if (isset($this->input['filters'])) {
            $non_member_name = $this->input['filters']['non_member_name'] ?? '';
            unset($this->input['filters']['non_member_name']);
        }


        $obj = $this->tenantDB()->table('chsone_credit_accounts as cr_acc')
            ->join(
                'chsone_nonmember_master as nonmembermaster',
                'cr_acc.account_id',
                '=',
                'nonmembermaster.nonmember_id'
            )
            ->where('cr_acc.account_context', 'nonmember')
            ->groupBy('cr_acc.account_id')
            ->orderByDesc('cr_acc.credit_account_id')
            ->selectRaw(
                'cr_acc.account_id AS id,
        cr_acc.account_id,
        cr_acc.account_name as non_member_name,
        nonmembermaster.first_name,
        nonmembermaster.last_name,
        ROUND(SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)), 2) AS total_cr,
        ROUND(SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)), 2) AS total_dr,
        ROUND(SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)), 2) AS total_refundable_dr,
        ROUND(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)), 2) AS total_refundable_cr,
        ROUND(SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)), 2) AS total_adjustable_dr,
        ROUND(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)), 2) AS total_adjustable_cr,
        FORMAT(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)), 2) AS total_adjustable,
        FORMAT(SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)), 2) AS total_refundable,
        FORMAT(SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)), 2) AS total_balance'
            );


        

        // if ($non_member_name) {
        //     $obj = $obj->where('nonmembermaster.first_name', 'like', '%' . $non_member_name . '%')
        //         ->orWhere('nonmembermaster.last_name', 'like', '%' . $non_member_name . '%')
        //         ->orWhere(DB::raw('CONCAT(nonmembermaster.first_name, " ", nonmembermaster.last_name)'), 'like', '%' . $non_member_name . '%');
        // }

        if ($non_member_name) {
            $obj = $obj->where('cr_acc.account_name', 'like', '%' . $non_member_name . '%');
        }

        $columns = [
            'nonmembermaster.first_name',
            'nonmembermaster.last_name',
            DB::raw('SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) AS total_cr'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) AS total_dr'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable_dr'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable_cr'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_dr'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable_cr'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "adjustable", cr_acc.amount, 0)) AS total_adjustable'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "cr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr" AND cr_acc.use_credit = "refundable", cr_acc.amount, 0)) AS total_refundable'),
            DB::raw('SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) - SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) AS total_balance')
        ];

        // … earlier in apply()
        // if ($searchTerm) {
        //     // 1) Filter first_name / last_name with where() (they’re not aggregates)
        //     $obj->where(function ($q) use ($searchTerm) {
        //         $q->where('nonmembermaster.first_name', 'like', "%{$searchTerm}%")
        //         ->orWhere('nonmembermaster.last_name', 'like', "%{$searchTerm}%")
        //         ->orWhere(DB::raw('CONCAT(nonmembermaster.first_name," ",nonmembermaster.last_name)'), 'like', "%{$searchTerm}%");
        //     })
        //     // 2) Now filter the SUM(...) columns with proper havingRaw SQL + bindings-array
        //     ->havingRaw(
        //         'SUM(IF(cr_acc.transaction_type = "cr", cr_acc.amount, 0)) LIKE ?',
        //         ["%{$searchTerm}%"]
        //     )
        //     ->orHavingRaw(
        //         'SUM(IF(cr_acc.transaction_type = "dr", cr_acc.amount, 0)) LIKE ?',
        //         ["%{$searchTerm}%"]
        //     );
        // }

        if ($searchTerm) {
            $obj->havingRaw("cr_acc.account_name LIKE ?", ['%' . $searchTerm . '%']);
        }

        


        $count = $obj->get()->count();

        $obj = $obj->offset($offset);
        $obj = $obj->limit($per_page);

        $result = $obj->get();

        // Compute summary totals
        $totals = $result->reduce(function ($carry, $item) {
            $ref = (float) str_replace(',', '', $item->total_refundable);
            $adj = (float) str_replace(',', '', $item->total_adjustable);
            $carry['total_summary_refundable']  += $ref;
            $carry['total_summary_adjustable']  += $adj;
            return $carry;
        }, [
            'total_summary_refundable' => 0.0,
            'total_summary_adjustable' => 0.0,
        ]);

        // Inject id for formatting
        $totals = ['id' => 1] + $totals;
        $totals['total_summary_refundable'] = round($totals['total_summary_refundable'], 2);
        $totals['total_summary_adjustable'] = round($totals['total_summary_adjustable'], 2);

        $route = Route::current();
        $routeUri = $route->uri();

        if ($routeUri == 'api/admin/credit-accounts/nonMemberAdvances/report') {
            $this->data = [
                $this->format($result->toArray()),
                [$totals]
            ];
        } else {
            $this->data = [
                $this->format($result->toArray()),
            ];       
        }

        


        // $this->data = $this->format($result->toArray());
        $this->meta['schema'] = $this->schema;
        if ($routeUri == 'api/admin/credit-accounts/nonMemberAdvances/report')
        {
            $this->meta['schema'] = $this->schema1;
        }
        $this->meta['pagination']['total'] = $count;
    }

    public function concat($a, $b)
    {
        return $a . " " . $b;
    }

    public function checkDisable($cr, $dr)
    {
        $value = $cr - $dr;
        if ($value > 0) {
            return false;
        } else {
            return true;
        }
        // return $value ? true : false;
    }
}
