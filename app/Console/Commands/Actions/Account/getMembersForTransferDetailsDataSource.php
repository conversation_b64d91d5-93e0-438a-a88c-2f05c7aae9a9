<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Actions\BaseDataSource;

use App\Console\Commands\Action;
use Illuminate\Support\Facades\DB;

class getMembersForTransferDetailsDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:getMembersForTransferDetails  {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for viewing member shares';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_id = $this->input['id'] ?? null;
        $soc_id = $this->input['company_id'] ?? null;
        // check if unit_id is provided
        if (!$unit_id) {
            $this->status = 'error';
            $this->statusCode = '404';
            $this->message = 'Unit ID is required';
            $this->data = [];
            return;
        }

        // check unit_id is valid or not
        $unitDetails = $this->tenantDB()
            ->table('chsone_units_master')
            ->where('unit_id', $unit_id)
            ->where('soc_id', $soc_id)
            ->first();

        if (empty($unitDetails)) {
            $this->status = 'error';
            $this->statusCode = '404';
            $this->message = 'Unit not found';
            $this->data = [];
            return;
        }


        // fetch member details for the unit
        $obj = $this->tenantDB()->table('chsone_members_master AS memberMaster')
            ->select(
                'units.unit_id as id',
                DB::raw("CONCAT(units.soc_building_name, ' / ', units.unit_flat_number) as unit_name"),
                'units.fk_unit_category_id as unit_category_id',
                'units.unit_category',
                'units.soc_building_floor',
                DB::raw("CAST(units.chargeable AS UNSIGNED) as chargeable"),
                'memberMaster.id as member_id',
                DB::raw("CONCAT(memberMaster.member_first_name,' ',memberMaster.member_last_name) as member_name"),
                DB::raw("CONCAT(memberMaster.member_first_name, ' ', memberMaster.member_last_name, ' (', units.soc_building_name, '-', units.unit_flat_number, ')') as display_member_name"),
                'memberMaster.member_mobile_number',
                'memberMaster.member_email_id',
                DB::raw("TRIM(memberMaster.member_email_id) as member_email_id"),
            )
            ->leftJoin('chsone_units_master AS units', 'units.unit_id', '=', 'memberMaster.fk_unit_id')
            ->leftJoin('chsone_member_type_master AS memberType', 'memberType.member_type_id', '=', 'memberMaster.member_type_id')
            ->whereIn('memberType.member_type_name', ['Associate', 'Nominal'])
            ->where('memberMaster.status', 1)
            ->where('memberMaster.soc_id', $soc_id)
            ->where('units.is_allotted', '1')
            ->where('units.unit_id', $unit_id)
            ->first();

        $this->data = $obj;
    }
}
