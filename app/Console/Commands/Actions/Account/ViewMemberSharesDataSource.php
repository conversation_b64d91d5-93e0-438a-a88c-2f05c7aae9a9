<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Actions\BaseDataSource;

use App\Console\Commands\Action;

class ViewMemberSharesDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:viewMemberShares  {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data for viewing member shares';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $unit_id = $this->input['id'] ?? null;
        if (!$unit_id) {
            $this->status = 'error';
            $this->statusCode = '404';
            $this->message = 'ID is required';
            $this->data = [];
            return;
        }

        $soc_id = $this->input['company_id'];
        $unit_info = $this->tenantDB()
            ->table('chsone_units_master')
            ->where('soc_id', $soc_id)
            ->where('unit_id', $unit_id)
            ->where('status', 1)
            ->first();
        if (!empty($unit_info)) {
            $unitDetls = (array) $unit_info;
            $viewShareDetls = $this->getShareDetailsByUnitIdAction($soc_id, $unit_id);
            
            if(empty($viewShareDetls)){
                $viewShareDetls = $unitDetls;
            }
    }
    $this->data = $viewShareDetls;
}

public function getShareDetailsByUnitIdAction($soc_id, $unit_id)
{
    $arrResponse = [];
    // Fetch the member_share_certificate record
    $result = $this->tenantDB()
    ->table('chsone_member_share_certificate')
        ->where('soc_id', $soc_id)
        ->where('unit_id', $unit_id)
        ->where('status', 1)
        ->first();
    if (!empty($result)) {
        $arrResponse['member_share_certificate'] = (array) $result;
        $arrResponse['iregister'] = (array) $result;
        $shareId = $arrResponse['iregister']['id'];
        // Assuming these methods exist and return arrays
        $arrResponse['nominees'] = $this->getNomineeDetailsByShareIdAction($shareId);
        $arrResponse['cashbookfolios'] = $this->getCashBookFolioDetailsByShareIdAction($shareId);
    }
    // Return the array (or any other format you prefer
    return $arrResponse;
}

public function getNomineeDetailsByShareIdAction($share_certificate_id)
{
    $arrResponse = [];
    $result = $this->tenantDB()
        ->table('chsone_iregister_nominees')
        ->where('share_certificate_id', $share_certificate_id)
        ->where('status', 1)
        ->get();
    if (!$result->isEmpty()) {
        $arrResponse = $result->toArray();
    }
    return $arrResponse;
}

public function getCashBookFolioDetailsByShareIdAction($share_certificate_id)
{
    $arrResponse = [];
    $result = $this->tenantDB()
        ->table('chsone_cashbook_folios as cashbookfolios')
        ->select([
            'cashbookfolios.id',
            'cashbookfolios.cashbook_folio_no',
            'cashbookfolios.source_cashbook_folio_id',
            'cashbookfolios.created_date',
            'sourcecashbookfolios.cashbook_folio_no as source_cashbook_folio_no',
        ])
        ->leftJoin('chsone_cashbook_folios as sourcecashbookfolios', 'sourcecashbookfolios.id', '=', 'cashbookfolios.source_cashbook_folio_id')
        ->where('cashbookfolios.share_certificate_id', $share_certificate_id)
        ->orderByDesc('cashbookfolios.created_date')
        ->get();
        
    if (!$result->isEmpty()) {
        $arrResponse = $result->toArray();
    }
    return $arrResponse;
}
}
