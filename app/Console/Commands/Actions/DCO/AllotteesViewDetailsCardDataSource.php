<?php

namespace App\Console\Commands\Actions\DCO;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use Illuminate\Support\Facades\Route;

class AllotteesViewDetailsCardDataSource extends Action
{
    protected $signature = 'datasource:allotteesViewDetailsCard {flowId} {parentId} {input}';

    protected $description = 'Allottees Details';

    public function apply()
    {
        $id = $this->input['id'];
        //check member id in chsone_members_master table present or not.
        $memberId = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $id)
            ->exists();
            
        if (!$memberId) {
            $this->status = 'error';
            $this->statusCode = 400;
            $this->message = 'Please provide a valid member id';
            return;
        }

        // get route to switch query
        $currentRoute = Route::current();

        // Get the route URI pattern (e.g., "member/register/{id}")
        $routeUri = $currentRoute->uri();

        if ($routeUri == 'api/admin/member/register/{id}') {
            $memberDetails = $this->tenantDB()->table('chsone_members_master AS member_master')
                ->selectRaw('
        member_master.id,
        member_master.salute,
        member_master.fk_unit_id,
        member_master.member_type_id,
        units.unit_flat_number,
        units.unit_type,
        units.soc_building_id,
        units.soc_building_name,
        units.soc_building_floor as member_building_floor,
        units.is_occupied,
        units.is_allotted,
        units.fk_unit_category_id,
        units.unit_category,
        member_type.member_type_name,
        member_master.member_first_name,
        member_master.member_last_name,
        CONCAT(member_master.member_first_name, " ", member_master.member_last_name, "(", unit_flat_number, ")") AS display_name,
        trim(member_master.member_email_id) AS member_email_id,
        COALESCE(member_master.member_contact_number, "") AS member_contact_number,
        trim(member_master.member_mobile_number) AS member_mobile_number,
        member_master.effective_date,
        member_master.gstin,
        member_master.unique_code,
        COALESCE(member_master.member_intercom, "N/A") AS member_intercom,
        member_master.member_dob,
        member_master.member_gender
    ')
                ->join('chsone_units_master AS units', 'units.unit_id', '=', 'member_master.fk_unit_id')
                ->join(
                    'chsone_member_type_master AS member_type',
                    'member_master.member_type_id',
                    '=',
                    'member_type.member_type_id'
                )
                ->where('member_master.id', $id)
                ->first();
        } else {
            $memberDetails = $this->tenantDB()->table('chsone_members_master AS member_master')
                ->selectRaw('
        member_master.id,
        member_master.salute,
        member_master.fk_unit_id,
        member_master.member_type_id,
        units.unit_flat_number,
        units.unit_type,
        units.soc_building_id,
        units.soc_building_name,
        units.soc_building_floor as member_building_floor,
        units.is_occupied,
        units.is_allotted,
        units.fk_unit_category_id,
        units.unit_category,
        member_type.member_type_name,
        member_master.member_first_name,
        member_master.member_last_name,
        CONCAT(member_master.member_first_name, " ", member_master.member_last_name, "(", unit_flat_number, ")") AS display_name,
        trim(member_master.member_email_id) AS member_email_id,
        COALESCE(member_master.member_contact_number, "") AS member_contact_number,
        trim(member_master.member_mobile_number) AS member_mobile_number,
        member_master.effective_date,
        member_master.gstin,
        member_master.unique_code,
        COALESCE(member_master.member_intercom, "N/A") AS member_intercom, -- Handle NULL intercom
        member_master.member_dob,
        CASE 
            WHEN member_master.member_gender = "M" THEN "Male"
            WHEN member_master.member_gender = "F" THEN "Female"
            ELSE "Unknown"
        END AS member_gender -- Transform gender
    ')
                ->join('chsone_units_master AS units', 'units.unit_id', '=', 'member_master.fk_unit_id')
                ->join(
                    'chsone_member_type_master AS member_type',
                    'member_master.member_type_id',
                    '=',
                    'member_type.member_type_id'
                )
                ->where('member_master.id', $id)
                ->first();
        }

        // check if member_email_id and member_mobile_number is empty or null then set it to null
        if (empty($memberDetails->member_email_id)) {
            $memberDetails->member_email_id = null;
        }
        if (empty($memberDetails->member_mobile_number)) {
            $memberDetails->member_mobile_number = null;
        }

        $this->data = $memberDetails;
    }
}
