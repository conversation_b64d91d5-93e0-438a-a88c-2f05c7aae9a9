<?php

namespace App\Console\Commands\Actions\DCO\Allottee;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class approveMemberChangeRequestDataSource extends Action
{
    protected $signature = 'datasource:approveMemberChangeRequest {flowId} {parentId} {input}';

    protected $description = 'Approve Member Change Request Data Source';

    public function apply()
    {
        $member_id = $this->input['id'];
        $user_id = $this->input['user_id'] ?? null;

        // Fetch member change request
        $fetchDetails = $this->tenantDB()->table('chsone_member_change_requests')
        ->select('id', 'user_id', 'member_id', 'member_first_name', 'member_last_name', 'otp', 'changerequeststatus', 'member_email_id', 'member_mobile_number')
        ->where('member_id', $member_id)
        ->where('user_id', $user_id)
        ->first();

        // Check if the member change request exists
        if (!$fetchDetails) {
            $this->status = 'error';
            $this->message = 'No details found for this member change request';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }

        if($fetchDetails->changerequeststatus == 1) {
            $this->status = 'error';
            $this->message = 'Member change request already approved';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // Update the member change request changerequeststatus to 1
        $updateStatus = $this->tenantDB()->table('chsone_member_change_requests')
            ->where('member_id', $member_id)
            ->where('user_id', $user_id)
            ->update([
                'changerequeststatus' => 1
            ]);

        if (!$updateStatus) {
            $this->status = 'error';
            $this->message = 'Failed to update member change request status';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // firstly fetch the previous member master entry with member_id and then add entry previous member master entry into member master history table
        $previousMember = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $member_id)
            ->first();

        if (!$previousMember) {
            $this->status = 'error';
            $this->message = 'Failed to fetch previous member details';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

         // Backup existing member data into history table
        $insertHistroy = $this->tenantDB()->table('chsone_members_master_history')->insert([
            'action' => 'update',
            'id' => $previousMember->id,
            'member_type_id' => $previousMember->member_type_id,
            'soc_id' => $previousMember->soc_id,
            'user_id' => $previousMember->user_id,
            'fk_unit_id' => $previousMember->fk_unit_id,
            'member_first_name' => $previousMember->member_first_name,
            'member_last_name' => $previousMember->member_last_name,
            'member_email_id' => $previousMember->member_email_id,
            'member_email_id_sec' => $previousMember->member_email_id_sec,
            'member_contact_number' => $previousMember->member_contact_number,
            'member_mobile_number' => $previousMember->member_mobile_number,
            'member_intercom' => $previousMember->member_intercom,
            'member_gender' => $previousMember->member_gender,
            'member_dob' => $previousMember->member_dob,
            'member_occupation' => $previousMember->member_occupation,
            'effective_date' => $previousMember->effective_date,
            'cancel_date' => $previousMember->cancel_date,
            'is_tenant' => $previousMember->is_tenant,
            'approved' => $previousMember->approved,
            'is_user_created' => $previousMember->is_user_created,
            'status' => $previousMember->status,
            'created_date' => $previousMember->created_date,
            'created_by' => $previousMember->created_by,
            'updated_date' => $previousMember->updated_date,
            'updated_by' => $previousMember->updated_by
        ]);

        if (!$insertHistroy) {
            $this->status = 'error';
            $this->message = 'Failed to insert previous member details into history';
            $this->statusCode = 400;
            $this->data = [];
            return;
        }

        // now update the member master table with the new details from the member change request
        $updated = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $member_id)
            ->update([
                'user_id' => $fetchDetails->user_id,
                'member_first_name' => $fetchDetails->member_first_name,
                'member_last_name' => $fetchDetails->member_last_name,
                'member_email_id' => $fetchDetails->member_email_id,
                'member_mobile_number' => $fetchDetails->member_mobile_number,
                'user_account_id' => $fetchDetails->user_id,
                'member_status' => null,
                'approved' => 1,
                'is_user_created' => 1,
                'status' => 1,
                'updated_date' => now(),
                'updated_by' => $user_id ?? 0
            ]);

        if($updated) {
            $this->status = 'success';
            $this->message = 'Member change request approved successfully';
            $this->statusCode = 200;
            $this->data = [];
        } else {
            $this->status = 'error';
            $this->message = 'Failed to approve member change request';
            $this->statusCode = 400;
            $this->data = [];
        }

    }
}