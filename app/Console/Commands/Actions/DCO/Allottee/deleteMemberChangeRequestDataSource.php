<?php

namespace App\Console\Commands\Actions\DCO\Allottee;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class deleteMemberChangeRequestDataSource extends Action
{
    protected $signature = 'datasource:deleteMemberChangeRequest {flowId} {parentId} {input}';

    protected $description = 'Delete Member Change Request Data Source';

    public function apply()
    {
        try{
            $member_id = $this->input['id'];
            $user_id = $this->input['user_id'];

            $obj = $this->tenantDB()->table('chsone_member_change_requests')
            ->select('id', 'user_id', 'member_id', 'member_first_name', 'member_last_name', 'otp', 'changerequeststatus', 'member_email_id', 'member_mobile_number')
            ->where('member_id', $member_id)
            ->where('user_id', $user_id);

            // Check if the member change request exists
            if (!$obj->exists()) {
                $this->status = 'error';
                $this->message = 'No details found for this member change request';
                $this->statusCode = 404;
                $this->data = [];
                return;
            }

            // Delete the entry
            $deleted = $this->tenantDB()->table('chsone_member_change_requests')
                ->where('member_id', $member_id)
                ->where('user_id', $user_id)
                ->delete();

            if($deleted) {
                $this->status = 'success';
                $this->message = 'Member Removed/Deleted Successfully';
                $this->statusCode = 200;
                $this->data = [];
            } else {
                $this->status = 'error';
                $this->message = 'Member Removal/Deletion Failed';
                $this->statusCode = 400;
                $this->data = [];
            }
        } catch (\Exception $e) {
            $this->status = 'error';
            $this->message = $e->getMessage();
            $this->statusCode = 500;
            $this->data = [];
        }
    }
}