# Society Bill Generation - System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Web Interface]
        API_CLIENT[API Client]
    end
    
    subgraph "API Layer"
        ROUTES[routes/api.php]
        CONTROLLER[IncomeDetailsController]
    end
    
    subgraph "Business Logic Layer"
        subgraph "Workflows"
            WF1[generateBulkBillWorkflow]
            WF2[generateManualBillWorkflow]
        end
        
        subgraph "Data Sources"
            DS1[GenerateBulkBillDataSource]
            DS2[GenerateManualBillDataSource]
        end
        
        subgraph "Core Models"
            MODEL1[ChsoneInvoiceGeneration]
            MODEL2[IncomeUnitInvoice]
            MODEL3[IncomeInvoiceSetting]
        end
    end
    
    subgraph "Database Layer"
        subgraph "Invoice Tables"
            T1[income_unit_invoice]
            T2[income_invoice_particulars]
            T3[income_invoice_payment_tracker]
        end
        
        subgraph "Configuration Tables"
            T4[income_invoice_settings]
            T5[income_invoice_general_settings]
            T6[income_late_payment_charges]
        end
        
        subgraph "Master Data"
            T7[chsone_units_master]
            T8[chsone_members_master]
            T9[chsone_societies_master]
        end
    end
    
    UI --> API_CLIENT
    API_CLIENT --> ROUTES
    ROUTES --> CONTROLLER
    
    CONTROLLER --> WF1
    CONTROLLER --> WF2
    
    WF1 --> DS1
    WF2 --> DS2
    
    DS1 --> MODEL1
    DS2 --> MODEL1
    
    MODEL1 --> MODEL2
    MODEL1 --> MODEL3
    
    MODEL1 --> T1
    MODEL1 --> T2
    MODEL1 --> T3
    MODEL2 --> T4
    MODEL3 --> T5
    MODEL1 --> T6
    MODEL1 --> T7
    MODEL1 --> T8
    MODEL1 --> T9
    
    style UI fill:#e3f2fd
    style MODEL1 fill:#fff3e0
    style T1 fill:#e8f5e8
    style T4 fill:#fce4ec
```

## Data Flow Architecture

```mermaid
flowchart LR
    subgraph "Input Parameters"
        P1[bill_date]
        P2[start_date]
        P3[end_date]
        P4[action_type]
        P5[unit_id optional]
    end
    
    subgraph "Processing Engine"
        E1[Invoice Settings Loader]
        E2[Unit Eligibility Filter]
        E3[Charge Calculator]
        E4[Tax Engine]
        E5[Outstanding Handler]
    end
    
    subgraph "Output Generation"
        O1[Invoice Preview]
        O2[Invoice Database Record]
        O3[Payment Tracker Entry]
        O4[Tax Log Entries]
    end
    
    P1 --> E1
    P2 --> E1
    P3 --> E1
    P4 --> E1
    P5 --> E2
    
    E1 --> E2
    E2 --> E3
    E3 --> E4
    E4 --> E5
    
    E5 --> O1
    E5 --> O2
    E5 --> O3
    E5 --> O4
```

## Invoice Generation States

```mermaid
stateDiagram-v2
    [*] --> InitiateGeneration
    InitiateGeneration --> ValidateParameters
    ValidateParameters --> LoadSettings
    LoadSettings --> FetchUnits
    FetchUnits --> ProcessUnit
    
    ProcessUnit --> CalculateCharges
    CalculateCharges --> ApplyTaxes
    ApplyTaxes --> HandleOutstanding
    HandleOutstanding --> GeneratePreview
    
    GeneratePreview --> PreviewMode
    GeneratePreview --> GenerateMode
    
    PreviewMode --> [*]
    GenerateMode --> SaveToDatabase
    SaveToDatabase --> UpdateTrackers
    UpdateTrackers --> [*]
    
    ValidateParameters --> Error
    LoadSettings --> Error
    FetchUnits --> Error
    ProcessUnit --> Error
    Error --> [*]
```

## Key Configuration Points

```mermaid
graph TD
    subgraph "Invoice Frequency Settings"
        F1[Monthly]
        F2[Quarterly]
        F3[Half-yearly]
        F4[Yearly]
    end
    
    subgraph "Unit Eligibility Criteria"
        U1[is_allotted = 1]
        U2[chargeable = 1]
        U3[status = 1]
        U4[has_primary_member]
    end
    
    subgraph "Charge Types"
        C1[Maintenance Fee]
        C2[NOC Charges]
        C3[Parking Charges]
        C4[Common Area Charges]
        C5[Late Payment Charges]
    end
    
    subgraph "Tax Configuration"
        T1[GST Rules]
        T2[Tax Exemptions]
        T3[Tax Classes]
        T4[Tax Categories]
    end
    
    SETTINGS[Invoice Settings] --> F1
    SETTINGS --> F2
    SETTINGS --> F3
    SETTINGS --> F4
    
    FILTER[Unit Filter] --> U1
    FILTER --> U2
    FILTER --> U3
    FILTER --> U4
    
    CALCULATOR[Charge Calculator] --> C1
    CALCULATOR --> C2
    CALCULATOR --> C3
    CALCULATOR --> C4
    CALCULATOR --> C5
    
    TAX_ENGINE[Tax Engine] --> T1
    TAX_ENGINE --> T2
    TAX_ENGINE --> T3
    TAX_ENGINE --> T4
```